<!DOCTYPE html>
<html style="font-size: 37.5px;">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Expires" content="-1" />
    <meta http-equiv="pragram" content="no-cache" />
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="full-screen" content="yes">
    <meta name="x5-fullscreen" content="true">
    <title>广告举报</title>
    <style type="text/css">
        * {
            margin: 0;
            padding: 0;
        }

        .wrap {
            background-color: rgb(22, 24, 36);
            color: #FFFFFF;
            min-height: 100vh;
        }

        .title {
            width: 8.8rem;
            margin: 0 auto;
            font-size: 0.3733rem;
            color: #cccccc;
            line-height: 0.8rem;
            padding-top: 0.5333rem;
        }

        .seletx {
            color: #CCCCCC;
            width: 9rem;
            margin: 0.2666rem auto;
            line-height: 1.0666rem;
            font-size: 0.43rem;
        }

        .seletx>span {
            color: #ff0000;
        }

        .seletx input {
            margin-right: 0.2666rem;
        }

        #textarea {
            width: 8.5334rem;
            font-size: 0.43rem;
            line-height: 0.5866rem;
            padding: 0.1333rem;
            resize: none;
            border-radius: 0.16rem;
            border: none;
            outline: none;
        }

        .submit_box {
            width: 100%;
            height: 2.6rem;
            line-height: 2.6rem;
            text-align: center;
        }

        .footSubmit {
            display: inline-block;
            width: 8rem;
            height: 1.3333rem;
            /*margin: 20px auto;*/
            background-color: rgb(254, 43, 84);
            color: #FFFFFF;
            text-align: center;
            line-height: 1.3333rem;
            font-size: 0.5333rem;
            /*margin-top: 40px;*/
            border-radius: 0.1066rem;
            /*margin-left: 8%;*/
        }

        .footSubmit>input {
            border: none;
        }

        .popup-wrap {
            position: fixed;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
        }

        .popup-box {
            width: 8rem;
            height: 8.8rem;
            background-color: rgb(255, 255, 255);
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            border-radius: 0.2666rem;
            background: url(./img/pic1.png) no-repeat;
            background-size: 100% 100%;
        }

        .popup-btn {
            width: 6rem;
            margin-top: 5.8666rem;
        }
    </style>
</head>

<body>
    <div class="wrap">
        <div class="title">你的举报将在12小时内受理，若举报成功会第一时间告知处理结果，请尽量提交完整的举报描述及材料。</div>
        <div class="seletx">请选择举报原因<span>*</span></div>
        <div class="seletx">
            <label onclick="checkedChange(1)"><input class="Fruit" name="Fruit1" type="radio"
                    value="未经同意，打开第三方应用" />未经同意，打开第三方应用 </label><br />
            <label onclick="checkedChange(2)"><input class="Fruit" name="Fruit1" type="radio"
                    value="未经同意，自动下载" />未经同意，自动下载 </label><br />
            <label onclick="checkedChange(3)"><input class="Fruit" name="Fruit1" type="radio"
                    value="令人恶心、反感或不适" />令人恶心、反感或不适 </label><br />
            <label onclick="checkedChange(4)"><input class="Fruit" name="Fruit1" type="radio"
                    value="标题或内容夸张，诱导点击" />标题或内容夸张，诱导点击</label><br />
            <label onclick="checkedChange(5)"><input class="Fruit" name="Fruit1" type="radio"
                    value="内容粗糙或不美观" />内容粗糙或不美观 </label><br />
            <label onclick="checkedChange(6)"><input class="Fruit" name="Fruit1" type="radio"
                    value="涉及侵权（知识产权等合法权益）" />涉及侵权（知识产权等合法权益） </label><br />
            <label onclick="checkedChange(7)"><input class="Fruit" name="Fruit1" type="radio"
                    value="对此类广告不感兴趣" />对此类广告不感兴趣 </label><br />
            <label onclick="checkedChange(8)"><input class="Fruit" name="Fruit1" type="radio"
                    value="感觉侵犯了我的隐私" />感觉侵犯了我的隐私 </label><br />
            <label onclick="checkedChange(0)"><input class="Fruit" name="Fruit1" type="radio" value="其他问题"
                    checked="checked" />其他问题 </label><br />
        </div>
        <div class="submit_box">
            <div class="footSubmit" onclick="submit2()">提交</div>
        </div>
    </div>
    <div class="popup-wrap">
        <div class="popup-box">
            <img src="./img/bt.png" class="popup-btn" onclick="hides()">
        </div>
    </div>
    <script src="./js/jquery.min.js"></script>
    <script src="./js/open.js"></script>
    <script type="text/javascript">
        // 上报模块功能=====start===========
        function getQueryString(e) {
            if ((e = e || location.href).split("?").length <= 1) return {};
            var t = e.split("?")[1];
            if (!t) return {};
            for (var n = {},
                i = 0,
                a = (t = t.split("&")).length; i < a; i++) {
                var o = t[i].split("=");
                n["" + o[0]] = o[1]
            }
            return n
        }

        function webTrack(action) {
            if (!action) {
                action = 'view'
            }
            var querys = getQueryString()
            var defualtData = webTrackData()
            webTrackRequest('webland', Object.assign(querys, defualtData, {
                'scenario': action,
            }))
        }

        function HexToStr(str) {
            var reg = /\%([0-9a-zA-Z]{2})/g
            var newStr = str
            while (res = reg.exec(str)) {
                newStr = newStr.replace(res[0], String.fromCharCode(parseInt(res[1], 16)))
            }
            return newStr
        }

        function getQueryVariable(variable) {
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == variable) {
                    return decodeURIComponent(pair[1]);
                }
            }
            return (false);
        }

        function webTrackData() {
            return {
                'pkg': HexToStr(getQueryVariable('pkg')),
                'page': "",
                'lid': getQueryVariable('link_id'),
                'brand': "",
                'event': 'landweb',
                'product': "",
                'uniqueid': "",
                'osVersionCode': "",
                'channelId': getQueryVariable('channelId'),
                'timesTamp': new Date().getTime()
            }
        }

        function hides() {
            document.querySelector('.popup-wrap').style.display = "none"
            setCookie("one_feedback", 1, 120 * 1000);
            window.location.replace("index404.html");
        }

        // 参数处理，对象拼接成表单提交形式
        function httpBuildParams(a, traditional) {
            var prefix,
                s = [],
                add = function (key, value) {
                    value = (typeof value == 'function') ? value() : (value == null ? "" : value);
                    s[s.length] = encodeURIComponent(key) + "=" + encodeURIComponent(value);
                };
            if (traditional === undefined) {
                traditional = false;
            }
            if (Array.isArray(a)) {
                a.map(function (value) {
                    add(value.name, value.value);
                })
            } else if (typeof a === 'object') {
                for (var i in a) {
                    add(i, a[i]);
                }
            } else {
                for (prefix in a) {
                    buildParams(prefix, a[prefix], traditional, add);
                }
            }
            return s.join("&").replace(/%20/g, "+");
        }

        function webTrackRequest(store, data) { }

        // 上报模块功能============end=============
        var reason = 0;

        function resize() {
            document.documentElement.style.fontSize = window.innerWidth / 10 + 'px';
        }

        function checkedChange(e) {
            reason = e
        }
        resize()
        window.onresize = function () {
            resize()
        }

        function submit2() {
            if (getCookie('lock') == 1) {
                return;
            }
            //加锁防止重复执行
            setCookie('lock', 1, 3500);
            webTrack(`complaintSuccess_${reason}`);
            $(".popup-wrap").css("display", "flex");
        }
    </script>
</body>

</html>