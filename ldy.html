<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Expires" content="-1" />
    <meta http-equiv="pragram" content="no-cache" />
    <meta name="referrer" content="unsafe-url">
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="full-screen" content="yes">
    <meta name="x5-fullscreen" content="true">
    <title></title>

    <!-- 最精简的内联样式，只保留调起阶段必需的样式 -->
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #fff;
        }
        
        .hidden {
            display: none !important;
        }
    </style>

    <script>
        // ========== 最精简的调起逻辑 ==========

        // 核心全局变量
        var pkg, report_key, page, channel, userEntry, uctrackid, configId;
        var pageHide = false;
        var dataKey = "my-router-btn";

        // 获取URL参数函数
        function getQueryVariable(variable) {
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == variable) {
                    return decodeURIComponent(pair[1]);
                }
            }
            return false;
        }

        // 初始化核心参数
        function initCoreParams() {
            pkg = getQueryVariable('pkg');
            report_key = getQueryVariable('report_key');
            page = getQueryVariable('page');
            channel = getQueryVariable('channel');
            userEntry = getQueryVariable('userEntry');
            uctrackid = getQueryVariable('uctrackid');
            configId = getQueryVariable('configId');
        }

        // 构建参数字符串
        function httpBuildParams() {
            return 'userEntry=' + userEntry + '&channel=' + channel + '&uctrackid=' + uctrackid + "&appid=" + report_key + "&configId=" + configId;
        }

        // 点击跳转函数
        function clickHap() {
            window.location.href = 'hap://app/' + pkg + '?' + httpBuildParams();
        }

        // 唤醒落地页
        function jumpPage() {
            try {
                window.location.href = 'hap://app/' + pkg + '?' + httpBuildParams();
            } catch (e) {}
        }

        // 核心调起函数
        function launchFun() {
            jumpPage();

            try {
                if (typeof isEnvSupportRouter !== 'undefined') {
                    isEnvSupportRouter(function(bAvailable) {
                        if (bAvailable) {
                            setTimeout(() => {
                                if (!pageHide && typeof routeToQuickapp !== 'undefined') {
                                    routeToQuickapp(dataKey);
                                }
                            }, 300);
                        }
                    });
                }
            } catch (e) {}
        }

        // 简化的数据上报函数
        function dataReport(event) {
            if (!event || !report_key) return;

            var reportUrl = "https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/ad_report?appid_key=" + report_key + "&report_name=" + event;

            try {
                fetch(reportUrl).catch(function() {
                    // 静默处理错误，不影响调起逻辑
                });
            } catch (e) {
                // 兼容不支持fetch的环境
            }
        }

        // ========== 优化的加载流程 ==========

        // 第一步：立即加载外部脚本
        function loadExternalScript() {
            var script = document.createElement('script');
            script.src = 'https://jits5.heytapdownload.com/cms-jits-heytapmobi-com/iframe/qa_router.min.js';
            script.onload = function() {
                executelaunchLogic();
            };
            script.onerror = function() {
                executelaunchLogic();
            };
            document.head.appendChild(script);
        }

        // 第二步：执行调起逻辑
        function executelaunchLogic() {
            initCoreParams();
            dataReport('start');

            // 如果uctrackid不存在或者为空则上报
            if (!uctrackid) {
                dataReport('noUtrackid');
            }

            // 立即执行调起逻辑
            launchFun();
        }

        // 第三步：调起后加载页面功能
        function loadPageFeatures() {
            // 异步加载CSS
            var link = document.createElement('link');
            link.rel = 'stylesheet';
            link.type = 'text/css';
            link.href = './css/style.css';
            document.head.appendChild(link);

            // 异步加载main.js（包含所有页面功能）
            var script = document.createElement('script');
            script.src = './js/main.js';
            script.async = true;
            script.onload = function() {};
            document.head.appendChild(script);
        }

        launchFun();
        // 立即加载外部脚本并执行调起逻辑
        loadExternalScript();

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 调起后延迟加载页面内容和功能
            setTimeout(function() {
                loadPageFeatures();
            }, 100);
        });
    </script>
</head>

<body>
    <!-- 初始状态：完全空白的页面 -->
    <div id="page-content" class="hidden">
        <!-- 页面内容将通过JavaScript动态加载 -->
    </div>
</body>

</html>