* {
    margin: 0;
    padding: 0;
}

body {
    /* background-color: #0dcfe5; */
}

.page {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    /*margin-top: -20px;*/
}

.bg {
    width: 100%;
}

a {
    text-decoration: none;
    color: #666;
    font-size: 12px;
}

.text {
    color: #000;
    font-size: 4vw;
}

.box {
    margin-bottom: 3vw;
}

.ts {
    position: absolute;
    top: 17%;
    width: 46.5px;
    right: 0;
    z-index: 10;
}

.box-img {
    width: 100%;
}

#page-back {
    width: 15vw;
    height: 8vw;
    line-height: 8vw;
    color: #fff;
    font-size: 4vw;
    text-align: center;
    background-color: #029CFE;
    border-top-right-radius: 4vw;
    border-bottom-right-radius: 4vw;
    position: absolute;
    left: 0;
    top: 2vw;
    display: none;
}

.btn-box {
    width: 100%;
    position: absolute;
    top: 139vw;
    left: 0;
    text-align: center;
}

.btn {
    /* width: 294.5px; */
    width: 80vw;
    border: none;
    color: #fff;
}

.an_scale {
    animation-name: Scale;
    animation-iteration-count: infinite;
    animation-duration: 1500ms;
    animation-fill-mode: none;
    animation-timing-function: linear;
}

@keyframes Scale {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.appdata {
    color: #ffffff;
    font-size: 12px;
    position: absolute;
    top: -20px;
    left: 0px;
    z-index: 999;
    width: 100%;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
}

.appinfo {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    transform: scale(0.8);
    text-align: center;
    flex-wrap: wrap;
}

.appinfo span {
    display: inline-block;
    padding: 0 4px;
    font-size: 14px;
    color: #666;
}

.progress {
    position: absolute;
    top: 168.8vw;
    left: 50%;
    margin-left: -147.25px;
    width: 294.5px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.progress-bg {
    width: 294.5px;
    height: 15px;
    background: #fff8e1;
    border-radius: 100px;
}

.progress-t {
    width: 0px;
    height: 15px;
    background: linear-gradient(89deg, #ffffff 0%, #ff0303 100%);
    border-radius: 100px;
    box-shadow: 0px 0px 10px 0px #ffffff inset;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    animation: lenprogress 2s;
    animation-fill-mode: forwards;
}

.progress-t span {
    width: 9px;
    height: 9px;
    background: #ffd3e0;
    border-radius: 50%;
}

.progress-c {
    width: 294.5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-c span {
    font-size: 13px;
    color: #fffdf5;
}

@keyframes runprogress {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(230px);
    }
}

@keyframes lenprogress {
    from {
        width: 0px;
    }
    to {
        width: 88%;
    }
}

.progress-s {
    width: 294.5px;
    height: 15px;
    display: flex;
    padding: 0 4px;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    top: 0;
}

.progress-s span {
    width: 9px;
    height: 9px;
    background: #ffd3e0;
    border-radius: 50%;
}

.progress-run {
    position: absolute;
    left: 0;
    top: 15px;
    font-size: 12px;
    color: #fffdf5;
    animation: runprogress 2s;
    animation-fill-mode: forwards;
}

.endTime {
    position: absolute;
    top: 92%;
    left: 0;
    text-align: center;
    color: #fff;
    font-size: 13px;
    width: 100%;
}

.bottom-Tips {
    color: #ffadcb;
    font-size: 10px;
    width: 100%;
    text-align: center;
    position: absolute;
    bottom: 10px;
}