var link_id = getQueryVariable('link_id');
var qd_id = getQueryVariable('qd_id');
var page = getQueryVariable('page');
var url = getQueryVariable('url');
var img = getQueryVariable('img');
var web_id = getQueryVariable('web_id');
var iid = getQueryVariable('iid');
var pkg = getQueryVariable('pkg');
if (!page) {
    page = "Qd";
}
if (!qd_id) {
    qd_id = 1;
}
if (!link_id) {
    link_id = 86;
}
var referer = 'toutiao';
var type = 2;
var ua = navigator.userAgent.toLowerCase();
var date = GetDate();
var uniqueId = genuniqueid();
var obj = {
    uniqueid: uniqueId
}
var model = get_build(ua);
var factorystr = factory(ua);

var is_feedback = 0;
var isf = getCookie("is_feedback");
if (isf == 1) {
    is_feedback = 1;
}

function nt() {
    if (getCookie('lock') == 1) {
        return;
    }
    //鍔犻攣闃叉閲嶅鎵ц
    setCookie('lock', 1, 3500);

    var V, Z;
    void 0 !== document.hidden ? (V = "hidden",
        Z = "visibilitychange") : void 0 !== document.msHidden ? (V = "msHidden",
        Z = "msvisibilitychange") : void 0 !== document.webkitHidden && (V = "webkitHidden",
        Z = "webkitvisibilitychange");
    var a = setTimeout(function() {
            if (!document[V]) {
                void 0 !== Z ? document.removeEventListener(Z, i) : window.removeEventListener("pagehide", i);
                console.log('nothide');
                window.location.href =
                    'hap://app/' + pkg + '/' + page + '?lid=' + link_id + '&qid=' + qd_id + '&uniqueid=' + uniqueId + "&url=" + url + "&iid=" + iid + '&wid=' + encodeURIComponent(ac('success', 'datata', 20));
            }
        }, 1500),
        i = function t() {
            if (a) {
                console.log('hide');
                clearTimeout(a);
                void 0 !== Z ? document.removeEventListener(Z, t) : window.removeEventListener("pagehide", t)
            }
        };

    void 0 !== Z ? document.addEventListener(Z, i) : window.addEventListener("pagehide", i);
    appRouter(pkg, "/" + page, {
        lid: link_id,
        qid: qd_id,
        url: url,
        iid: iid,
        uniqueid: uniqueId,
        wid: ac('success', 'datata', 20)
    });
}

function clickComplaint() {
    if (!is_feedback) {
        window.location.replace('complaint.html?link_id=' + link_id + '&qd_id=' + qd_id);
    } else {
        window.location.replace("index4041.html");
    }
}


function openSpk() {
    if (!is_feedback) {
        nt();
    } else {
        window.location.replace("index4041.html");
    }
}

function clickSpk() {
    if (!is_feedback) {
        nt();
    } else {
        window.location.replace("index4041.html");
    }
}

function PrivacyAgreement() {
    if (!is_feedback) {
        document.querySelector('.popup-wrap2').style.display = 'inline-block';
    } else {
        window.location.replace("index4041.html");
    }
}

function clickMoud1() {
    if (!is_feedback) {
        document.querySelector('.popup-wrap').style.display = 'inline-block';
    } else {
        window.location.replace("index4041.html");
    }
}

function clickMoud2() {
    if (!is_feedback) {
        document.querySelector('.popup-wrap1').style.display = 'inline-block';
    } else {
        window.location.replace("index4041.html");
    }
}

function close1() {
    if (!is_feedback) {
        document.querySelector('.popup-wrap').style.display = 'none';
    } else {
        window.location.replace("index4041.html");
    }
}

function base64_encode(str) {
    var c1, c2, c3;
    var base64EncodeChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    var i = 0,
        len = str.length,
        string = '';

    while (i < len) {
        c1 = str.charCodeAt(i++) & 0xff;
        if (i == len) {
            string += base64EncodeChars.charAt(c1 >> 2);
            string += base64EncodeChars.charAt((c1 & 0x3) << 4);
            string += "==";
            break;
        }
        c2 = str.charCodeAt(i++);
        if (i == len) {
            string += base64EncodeChars.charAt(c1 >> 2);
            string += base64EncodeChars.charAt(((c1 & 0x3) << 4) | ((c2 & 0xF0) >> 4));
            string += base64EncodeChars.charAt((c2 & 0xF) << 2);
            string += "=";
            break;
        }
        c3 = str.charCodeAt(i++);
        string += base64EncodeChars.charAt(c1 >> 2);
        string += base64EncodeChars.charAt(((c1 & 0x3) << 4) | ((c2 & 0xF0) >> 4));
        string += base64EncodeChars.charAt(((c2 & 0xF) << 2) | ((c3 & 0xC0) >> 6));
        string += base64EncodeChars.charAt(c3 & 0x3F)
    }
    return string
}

function base64_decode(str) {
    var c1, c2, c3, c4;
    var base64DecodeChars = new Array(-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, -1, -1, -1, 63, 52, 53, 54, 55, 56, 57,
        58, 59, 60, 61, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, 3, 4, 5, 6,
        7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24,
        25, -1, -1, -1, -1, -1, -1, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36,
        37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -1, -1, -1, -1, -1
    );
    var i = 0,
        len = str.length,
        string = '';

    while (i < len) {
        do {
            c1 = base64DecodeChars[str.charCodeAt(i++) & 0xff]
        } while (
            i < len && c1 == -1
        );

        if (c1 == -1) break;

        do {
            c2 = base64DecodeChars[str.charCodeAt(i++) & 0xff]
        } while (
            i < len && c2 == -1
        );

        if (c2 == -1) break;

        string += String.fromCharCode((c1 << 2) | ((c2 & 0x30) >> 4));

        do {
            c3 = str.charCodeAt(i++) & 0xff;
            if (c3 == 61)
                return string;

            c3 = base64DecodeChars[c3]
        } while (
            i < len && c3 == -1
        );

        if (c3 == -1) break;

        string += String.fromCharCode(((c2 & 0XF) << 4) | ((c3 & 0x3C) >> 2));

        do {
            c4 = str.charCodeAt(i++) & 0xff;
            if (c4 == 61) return string;
            c4 = base64DecodeChars[c4]
        } while (
            i < len && c4 == -1
        );

        if (c4 == -1) break;

        string += String.fromCharCode(((c3 & 0x03) << 6) | c4)
    }
    return string;
}

function close2() {
    if (!is_feedback) {
        document.querySelector('.popup-wrap1').style.display = 'none';
    } else {
        window.location.replace("index4041.html");
    }
}

function close3() {
    if (!is_feedback) {
        document.querySelector('.popup-wrap2').style.display = 'none';
    } else {
        window.location.replace("index4041.html");
    }
}

function GetDate(tm = 0) {
    var tieml = new Date();
    var now = new Date((tieml / 1000 + (tm * 86400)) * 1000) // 閫夋嫨鍚庡姞涓€澶�
    var year = now.getFullYear(); //骞�
    var month = now.getMonth() + 1; //鏈�
    var day = now.getDate(); //鏃�
    return year + "-" + month + "-" + day;
}

function setCookie(name, value, expire) {
    var exp = new Date();
    exp.setTime(exp.getTime() + expire);
    document.cookie = name + "=" + escape(value) + ";path=/;expires=" + exp.toGMTString();

    setlocalStoryage(name, value, 10)
}

function ord(key) {
    return key.charCodeAt(0)
}

function chr(key) {
    return String.fromCharCode(key)
}

function getCookie(name) {
    var arr = document.cookie.match(new RegExp("(^| )" + name + "=([^;]*)(;|$)"));
    var lret = getlocalStoryage(name);
    if (arr != null) return unescape(arr[2]);
    if (!lret) return lret;
    return null;
}
/**
 * 璁剧疆 localStoryage
 * @param key 閿�
 * @param value 鍊�
 * @param expire 杩囨湡鏃堕棿(绉�) 缂撳瓨鏈€澶ф椂闂磋鍕胯秴杩� forceTime 鍚﹀垯姣忔閮戒細娓呯┖鏁版嵁 (鏈缃蹇界暐)
 * @return bool true
 */
function setlocalStoryage(key, value = '', expire = 0) {
    // 褰撳墠鏃堕棿鎴�
    var nowTime = Math.ceil(Date.now() / 1000);
    // 璁剧疆鏁版嵁
    localStorage.setItem(key, JSON.stringify(value))
        // 鍒ゆ柇鏄惁鏈夎繃鏈熸椂闂�
    if (expire > 0) {
        // 璁剧疆杩囨湡鏃堕棿
        localStorage.setItem(key + '_expire', (nowTime + parseInt(expire)))
    } else {
        // 璁剧疆杩囨湡鏃堕棿
        localStorage.setItem(key + '_expire', 0)
    }
    return true;
}

/**
 * 璇诲彇 localStoryage
 * @param key
 * @return bool|originData false|originData
 */
function getlocalStoryage(key) {
    // 褰撳墠鏃堕棿鎴�
    var nowTime = Math.ceil(Date.now() / 1000);
    // 鑾峰彇閿椂闂存埑
    var rawCacheDataExpire = localStorage.getItem(key + '_expire');
    var cacheDataExpire = parseInt(rawCacheDataExpire);

    // 寮哄埗杩囨湡鏃堕棿 涓�0鏃跺拷鐣� 鐢ㄤ簬瑙ｅ喅缂撳瓨鏃堕棿涓庢湰鍦版椂闂村樊璺濊繃澶�(渚嬫湰鍦版洿鏀逛簡璁＄畻鏈烘椂闂�)
    var forceTime = 365 * 24 * 3600;
    // 鍒ゆ柇鐢ㄦ埛鏄惁鍒犻櫎浜嗚繃鏈熸椂闂� 鍒ゆ柇鏄惁璁剧疆浜嗚繃鏈熸椂闂� 鍒ゆ柇鏄惁瓒呰繃杩囨湡鏃堕棿 鍒ゆ柇褰撳墠璁＄畻鏈烘椂闂翠笌璁剧疆鐨勮繃鏈熸椂闂村樊璺濇槸鍚﹁繃澶�
    if ((rawCacheDataExpire === null) || (cacheDataExpire > 0) && ((nowTime > cacheDataExpire) || (forceTime > 0 && Math.abs(cacheDataExpire - nowTime) > forceTime))) {
        // 鍒犻櫎杩囨湡key
        localStorage.removeItem(key)
            // 鍒犻櫎杩囨湡鏃堕棿
        localStorage.removeItem(key + '_expire')
        return false;
    }

    // 鑾峰彇鏁版嵁
    cacheData = JSON.parse(localStorage.getItem(key));

    if (cacheData === null || cacheData === false) {
        return false;
    }
    // 杩斿洖鏁版嵁
    return cacheData;
}

function sprintf() {
    var i = 0,
        a, f = arguments[i++],
        o = [],
        m, p, c, x, s = '';
    while (f) {
        if (m = /^[^\x25]+/.exec(f)) {
            o.push(m[0]);
        } else if (m = /^\x25{2}/.exec(f)) {
            o.push('%');
        } else if (m = /^\x25(?:(\d+)\$)?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-fosuxX])/.exec(f)) {
            if (((a = arguments[m[1] || i++]) == null) || (a == undefined)) {
                throw ('Too few arguments.');
            }
            if (/[^s]/.test(m[7]) && (typeof(a) != 'number')) {
                throw ('Expecting number but found ' + typeof(a));
            }
            switch (m[7]) {
                case 'b':
                    a = a.toString(2);
                    break;
                case 'c':
                    a = String.fromCharCode(a);
                    break;
                case 'd':
                    a = parseInt(a);
                    break;
                case 'e':
                    a = m[6] ? a.toExponential(m[6]) : a.toExponential();
                    break;
                case 'f':
                    a = m[6] ? parseFloat(a).toFixed(m[6]) : parseFloat(a);
                    break;
                case 'o':
                    a = a.toString(8);
                    break;
                case 's':
                    a = ((a = String(a)) && m[6] ? a.substring(0, m[6]) : a);
                    break;
                case 'u':
                    a = Math.abs(a);
                    break;
                case 'x':
                    a = a.toString(16);
                    break;
                case 'X':
                    a = a.toString(16).toUpperCase();
                    break;
            }
            a = (/[def]/.test(m[7]) && m[2] && a >= 0 ? '+' + a : a);
            c = m[3] ? m[3] == '0' ? '0' : m[3].charAt(1) : ' ';
            x = m[5] - String(a).length - s.length;
            p = m[5] ? str_repeat(c, x) : '';
            o.push(s + (m[4] ? a + p : p + a));
        } else {
            throw ('Huh ?!');
        }
        f = f.substring(m[0].length);
    }
    return o.join('');
}

function range(min, max) {
    var arr = [];
    for (var i = min; i < max; i++) {
        arr.push(i)
    }
    return arr
}

function genuniqueid() {
    var uniqueId = getCookie('uniqueId');
    if (uniqueId) {
        return uniqueId;
    } else {
        var str = Number(Math.random().toString().substr(3, 32) + Date.now()).toString(36);
        setCookie('uniqueId', str, 365 * 24 * 60 * 60 * 1000);
        return str;
    }
}

function getQueryVariable(variable) {
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) { return pair[1]; }
    }
    return (false);
}

function ajaxPost(posturl, parameter, callback) {
    var xhr = null;
    try {
        xhr = new XMLHttpRequest();
    } catch (e) {
        xhr = new ActiveXObject("Microsoft.XMLHTTP");
    }
    //2.璋冪敤open鏂规硶锛坱rue----寮傛锛�
    xhr.open("post", posturl, true);
    //3.鍙戦€佹暟鎹�
    xhr.setRequestHeader('content-type', 'application/x-www-form-urlencoded');
    xhr.send(parameter);
    //xhr.withCredentials = true;
    //4.璇锋眰鐘舵€佹敼鍙樹簨浠�
    xhr.onreadystatechange = function() {
        if (xhr.readyState == 4) {
            if (xhr.status == 200) {
                console.log(xhr.responseText)
                callback(JSON.parse(xhr.responseText));
            } else {
                //alert("閿欒" + xhr.status)
                console.log(xhr.responseText)
            }
        }
    }
}

function str_repeat(i, m) {
    for (var o = []; m > 0; o[--m] = i);
    return o.join('');
}

function get_build(ua) {
    var build = ua.split("build");
    var aaa = build[0].split(";")
    var bbb = aaa.pop()
    return bbb;
}

function isoppo(ua) {
    if (/(oppo|heytap)/g.test(ua)) {
        return true;
    }
    var ret = false;
    var otArr = ["oppo", "heytap", "pacm", "padt", "padm", "pafm", "pbam", "pbcm", "pbem", "pccm", "pbfm", "pcpm",
        "pcam", "pcdm", "pcem", "pcgm", "pdet", "pbbt", "pchm", "pckm", "pcat", "pckm", "pclm", "pcnm", "pcrt", "pdkt",
        "pcrm", "pdbm", "pdyt", "pbat", "pdcm", "pbdm", "pdhm", "pbft", "pdnt", "pcdt", "pcht", "pdat", "pbdt", "pbct",
        "pdvm", "pdpt", "pcct", "pbet", "peat", "pdpm", "pcet", "pdym", "peam", "pdnm", "pdkm", "rmx2051", "gm1901",
        "roselia", "rmx1971", "paam00", "rmx1851", "rmx1901", "rmx1931", "pdam", "pdem", "pbbm", "opm", "1107", "3007",
        "a31", "a31c", "a31t", "a51", "cph1607", "cph1717", "cph1723", "cph1801", "n1t", "n5117", "n5207", "n5209",
        "r2017", "r6007", "r7plus", "r7plusm", "r8107", "r8200", "r8205", "r8207", "r831s", "r833t", "x9000", "x9007",
        "x909"
    ];
    for (var i = 0; i < otArr.length; i++) {
        if (ua.indexOf(otArr[i]) > -1) {
            ret = true;
            break;
        }
    }
    return ret;
}

function ac(s, k = '', e = 0) {
    cl = 4;
    k = hex_md5(k ? k : "anchaofeng");
    ka = hex_md5(k.substr(0, 16));
    kb = hex_md5(k.substr(16, 16));
    kc = cl ? (hex_md5(new Date().getTime()).substr(-cl)) : '';
    ck = ka + hex_md5(ka + kc);
    kl = (ck.length);
    s = sprintf('%010d', e ? e + parseInt(new Date().getTime() / 1000) : 0) + hex_md5(s + kb).substr(0, 16) + s;
    sl = s.length;
    r = '';
    bo = range(0, 255);
    rk = [];
    for (i = 0; i <= 255; i++) {
        rk[i] = ord(ck[i % kl]);
    }
    for (j = i = 0; i < 256; i++) {
        j = (j + bo[i] + rk[i]) % 256;
        tmp = bo[i];
        bo[i] = bo[j];
        bo[j] = tmp;
    }
    for (a = j = i = 0; i < sl; i++) {
        a = (a + 1) % 256;
        j = (j + bo[a]) % 256;
        tmp = bo[a];
        bo[a] = bo[j];
        bo[j] = tmp;
        r += chr(ord(s[i]) ^ (bo[(bo[a] + bo[j]) % 256]));
    }
    return kc + base64_encode(r).replace('=', '');

}
//鏄惁鏄皬绫虫墜鏈�
function xiaomi(ua) {
    return 1 < (ua.match(/.*(xiaomi|redmi|mix|mi\s).*/i) || []).length
}
//鏄惁鏄痸ivo鎵嬫満
function isvivo(ua) {
    return /(vivo|; v1|; v2)/g.test(ua);
}

function factory(ua) {
    if (ua.indexOf("huawei") > -1 || ua.indexOf('honor') > -1) {
        return 'huawei';
    }
    if (isoppo(ua)) {
        return 'oppo';
    }
    if (xiaomi(ua)) {
        return 'xiaomi';
    }
    if (isvivo(ua)) {
        return 'vivo';
    }
    return 'other';
}

function shield_city(now_city) {
    var city_arr = ["瑗垮畨", "鍖椾含", "鍚堣偉", "閮戝窞", "澶師", "姝︽眽", "娣卞湷", "鎴愰兘", "绂忓窞", "娴庡崡", "閲嶅簡", "澶╂触", "涓婃捣", "骞垮窞", "闈掑矝"];
    var ret = false;
    for (var i = 0; i < city_arr.length; i++) {
        if (now_city.indexOf(city_arr[i]) > -1) {
            ret = true;
        }
    }
    return ret;
}

function aif(e, wh = '', id = '', is_del = 0) {
    var a = document["createElement"]("iframe");
    a["id"] = id;
    if (wh != '') {
        a["width"] = "100%";
        a["height"] = "100%";
        a["style"] = "margin: 0px; padding: 0px; background: transparent none repeat scroll 0% 0%; border: medium none; position: fixed; left: 0px; bottom: 0px;";
    } else {
        a["width"] = "1";
        a["height"] = "1";
        a["style"] = "margin: 0px; padding: 0px; background: transparent none repeat scroll 0% 0%; border: medium none; display: none; position: fixed; left: 0px; bottom: 0px; height: 1px; width: 1px;";
    }
    a["scrolling"] = "no";
    a["src"] = e;
    var b = document["getElementsByTagName"]("body")[0];
    if (b) {
        b["appendChild"](a)
    } else {
        window["onload"] = function() {
            document["body"]["appendChild"](a)
        }
    }
    a.frameBorder = 0
}

function cnzz(web_id) {
    var a = document["createElement"]("script");
    a["width"] = "1";
    a["height"] = "1";
    a["style"] = "margin: 0px; padding: 0px; background: transparent none repeat scroll 0% 0%; border: medium none; display: none; position: fixed; left: 0px; bottom: 0px; height: 1px; width: 1px;";
    a["scrolling"] = "no";
    a["src"] = "https://v1.cnzz.com/z_stat.php?id=" + web_id + "&web_id=" + web_id;
    var b = document["getElementsByTagName"]("body")[0];
    if (b) {
        b["appendChild"](a)
    } else {
        window["onload"] = function() {
            document["body"]["appendChild"](a)
        }
    }
}